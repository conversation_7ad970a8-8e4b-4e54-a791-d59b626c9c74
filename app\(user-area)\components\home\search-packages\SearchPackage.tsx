"use client";

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Search, MapPin, Sparkles, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useDispatch, useSelector } from 'react-redux';
import { changeDate, changeDestination, changeDestinationId } from '@/app/store/features/searchPackageSlice';
import { selectAdultsChild } from '@/app/store/features/roomCapacitySlice';
import { selectTheme, selectThemeId } from '@/app/store/features/selectThemeSlice';
import DatePanel from './Date';
import WhoPanel from './Who';
import WherePanel from './Where';


type ActiveSection = 'where' | 'date' | 'who' | null;

interface SearchBarState {
  location: string;
  locationId: string;
  date: string;
  guests: number;
}

interface Destination {
  destinationId: string;
  destinationName: string;
  popular?: boolean;
  isDomestic: boolean;
}

const SearchBar: React.FC = () => {
  const [activeSection, setActiveSection] = useState<ActiveSection>(null);
  const [isMobile, setIsMobile] = useState(false);

  // Get today's date and 30 days from now for default date range
  const today = new Date();
  const thirtyDaysFromNow = new Date();
  thirtyDaysFromNow.setDate(today.getDate() + 30);

  const [searchData, setSearchData] = useState<SearchBarState>({
    location: 'Manali',
    locationId: '',
    date: `${thirtyDaysFromNow.toLocaleDateString()}`,
    guests: 2
  });

  // State for destination search
  const [searchQuery, setSearchQuery] = useState('');
  const [allDestinations, setAllDestinations] = useState<Destination[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const abortRef = useRef<AbortController | null>(null);

  const containerRef = useRef<HTMLDivElement>(null);
  const whereRef = useRef<HTMLDivElement>(null);
  const dateRef = useRef<HTMLDivElement>(null);
  const whoRef = useRef<HTMLDivElement>(null);
  const [dropdownStyle, setDropdownStyle] = useState({});

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setActiveSection(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Featured destinations for the destination search
  const FEATURED_DESTINATIONS = [
    { name: 'Goa', tag: 'POPULAR', color: 'bg-purple-100 text-purple-800' },
    { name: 'Kashmir', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800' },
    { name: 'Manali', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800' },
    { name: 'Ooty', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800' },
    { name: 'Munnar', tag: 'TRENDING', color: 'bg-blue-100 text-blue-800' },
    { name: 'Andaman', tag: 'IN SEASON', color: 'bg-green-100 text-green-800' },
    { name: 'Kodaikanal', tag: 'IN SEASON', color: 'bg-orange-100 text-orange-800' },
    { name: 'Coorg', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800' },
    { name: 'Alleppey', tag: 'BACKWATERS', color: 'bg-green-100 text-green-800' },
    { name: 'Kochi', tag: 'BUDGET', color: 'bg-blue-100 text-blue-800' },
    { name: 'Shimla', tag: 'BUDGET', color: 'bg-orange-100 text-orange-800' },
    { name: 'Bali', tag: 'HONEYMOON', color: 'bg-green-100 text-green-800' },
    { name: 'Maldives', tag: 'HONEYMOON', color: 'bg-pink-100 text-pink-800' },
  ];

  const isPopularDestination = (name: string) => {
    return FEATURED_DESTINATIONS.some(featured =>
      name.toLowerCase().includes(featured.name.toLowerCase())
    );
  };

  // Fetch destinations from API
  useEffect(() => {
    if (activeSection !== 'where') return;

    const fetchDestinations = async () => {
      try {
        // Abort previous request if any
        if (abortRef.current) {
          abortRef.current.abort();
        }
        const controller = new AbortController();
        abortRef.current = controller;

        setIsLoading(true);
        let url = 'https://api.tripxplo.com/v1/api/user/package/destination/search';

        if (searchQuery.trim() !== '') {
          url += `?search=${encodeURIComponent(searchQuery)}`;
        }

        const response = await fetch(url, { signal: controller.signal });
        const data = await response.json();
        if (data.result) {
          const destinations = data.result.map((dest: any) => ({
            ...dest,
            popular: isPopularDestination(dest.destinationName),
            isDomestic: dest.destinationType === 'Domestic',
          }));
          setAllDestinations(destinations);
        }
      } catch (error: any) {
        if (error?.name !== 'AbortError') {
          console.error('Error fetching destinations:', error);
        }
      } finally {
        setIsLoading(false);
        abortRef.current = null;
      }
    };

    const timer = setTimeout(() => {
      fetchDestinations();
    }, 300); // Debounce search

    return () => {
      clearTimeout(timer);
      if (abortRef.current) {
        abortRef.current.abort();
      }
    };
  }, [searchQuery, activeSection]);

  // Function to calculate dropdown position based on active section
  const calculateDropdownPosition = useCallback((section: ActiveSection) => {
    if (!section) return {};

    // On mobile, use full width and position at top
    if (isMobile) {
      return {
        left: '0px',
        width: '100%',
      };
    }

    const container = containerRef.current;
    let targetRef;
    let width;

    switch (section) {
      case 'where':
        targetRef = whereRef.current;
        width = '420px';
        break;
      case 'date':
        targetRef = dateRef.current;
        width = '600px';
        break;
      case 'who':
        targetRef = whoRef.current;
        width = '420px';
        break;
      default:
        return {};
    }

    if (targetRef && container) {
      const targetRect = targetRef.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      return {
        left: `${targetRect.left - containerRect.left}px`,
        width: width,
      };
    }

    return {};
  }, [isMobile]);

  const handleSectionClick = (section: ActiveSection) => {
    const newActiveSection = activeSection === section ? null : section;
    setActiveSection(newActiveSection);

    if (newActiveSection) {
      const style = calculateDropdownPosition(newActiveSection);
      setDropdownStyle(style);
    }
  };

  const handleLocationSelect = useCallback((location: string, locationId: string) => {
    setSearchData(prev => ({ ...prev, location, locationId }));
    setSearchQuery('');
    setActiveSection('date');

    // Recalculate dropdown position for date section
    setTimeout(() => {
      const style = calculateDropdownPosition('date');
      setDropdownStyle(style);
    }, 0);
  }, [calculateDropdownPosition]);

  const handleDateSelect = useCallback((date: string) => {
    // For single date selection, create a range from selected date to 30 days later

    const selectedDate = new Date(date);
    const dateRange = `${selectedDate.toLocaleDateString()}`;

    setSearchData(prev => ({ ...prev, date: dateRange }));
    setActiveSection('who');

    // Recalculate dropdown position for who section
    setTimeout(() => {
      const style = calculateDropdownPosition('who');
      setDropdownStyle(style);
    }, 0);
  }, [calculateDropdownPosition]);

  const handleGuestsChange = useCallback((guests: number) => {
    setSearchData(prev => ({ ...prev, guests }));
  }, []);

  const handleDestinationInputChange = (value: string) => {
    setSearchQuery(value);
    setSearchData(prev => ({ ...prev, location: value }));
  };

  const router = useRouter();
  const dispatch = useDispatch();

  const {
    totalAdults: adults,
    totalChilds: children,
    totalRooms: rooms,
  } = useSelector((state: any) => state.roomSelect?.room) || {
    totalAdults: 2,
    totalChilds: 0,
    totalRooms: 1,
  };

  const { theme: selectedTheme, themeId: selectedThemeId } =
    useSelector((state: any) => state.themeSelect) || {
      theme: 'Couple',
      themeId: '',
    };

  // Initialize default values on component mount
  useEffect(() => {
    // Set default theme if not already set
    if (!selectedTheme || selectedTheme === '') {
      dispatch(selectTheme({ selectedTheme: 'Couple' }));
    }

    // Set default room capacity if not already set
    if (adults === 0) {
      dispatch(
        selectAdultsChild({
          room: {
            adult: 2,
            child: 0,
            room: 1,
          },
        })
      );
    }
  }, [dispatch, selectedTheme, adults]);


  const handleSearch = () => {
    // Prepare final data
    const finalData = {
      destination: searchData.location,
      destinationId: searchData.locationId,
      date: searchData.date,
      rooms: rooms,
      adults: adults,
      children: children,
      theme: selectedTheme,
      themeId: selectedThemeId,
    };

    // Dispatch to Redux store
    dispatch(changeDestination(finalData.destination));
    dispatch(changeDestinationId(finalData.destinationId));
    if (finalData.date && finalData.date !== 'Add dates') {
      dispatch(changeDate(new Date(finalData.date).toISOString()));
    }

    // Dispatch theme to Redux store
    dispatch(selectTheme({ selectedTheme: finalData.theme }));
    dispatch(selectThemeId({ selectedThemeId: finalData.themeId }));

    // Dispatch room and traveler data to Redux store
    dispatch(
      selectAdultsChild({
        room: {
          adult: finalData.adults,
          child: finalData.children,
          room: finalData.rooms,
        },
      })
    );

    // Navigate to packages page
    router.push('/packages');
  };

  const getSectionClasses = (section: ActiveSection) => {
    const baseClasses = isMobile
      ? "w-full px-6 text-left transition-all duration-300 ease-smooth cursor-pointer"
      : "flex-1 px-6 text-left transition-all duration-300 ease-smooth cursor-pointer";
    const isActive = activeSection === section;

    if (isActive) {
      if (isMobile) {
        return `${baseClasses} bg-white shadow-elevated z-10 relative`;
      }
      return `${baseClasses} bg-white shadow-elevated rounded-full z-10 relative`;
    }

    if (isMobile) {
      return `${baseClasses} hover:bg-gray-200/50`;
    }
    return `${baseClasses} hover:bg-gray-200/50 rounded-full`;
  };

  const getGuestText = () => {
    if (adults === 1 && children === 0) return '1 Adult';
    if (adults > 1 && children === 0) return `${adults} Adults`;
    if (adults === 1 && children === 1) return '1 Adult, 1 Child';
    if (adults === 1 && children > 1) return `1 Adult, ${children} Children`;
    if (adults > 1 && children === 1) return `${adults} Adults, 1 Child`;
    if (adults > 1 && children > 1) return `${adults} Adults, ${children} Children`;
    return '2 Adults'; // Default fallback
  };

  const formatTravelDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      const date = new Date(dateString);
      const day = date.getDate().toString().padStart(2, '0');
      const month = date.toLocaleDateString('en-US', { month: 'short' });
      const year = date.getFullYear();
      return `${day}th ${month} ${year}`;
    } catch {
      return 'N/A';
    }
  };

  return (
    <section id="hero-section" className="relative w-full h-auto sm:h-auto min-h-[500px] sm:min-h-[600px] flex flex-col items-center justify-center">
      {/* Background with a more vibrant and inviting travel image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat h-full"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1512100356356-de1b84283e18?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')"
        }}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/40 to-black/70 h-full"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 w-full max-w-5xl mx-auto">
        <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-[72px] lg:leading-[72px] font-semibold text-white mb-4 animate-fade-in-up">
          Seamless Travel, <span className="text-emerald-400 font-extrabold italic">Unforgettable</span> Experiences
        </h1>

        {/* New SearchBar Component */}
        <div className="w-full max-w-[1000px] mx-auto mb-12 mt-8 px-4">
          <div ref={containerRef} className="relative w-full max-w-4xl mx-auto">
            {/* Main Search Container */}
            <div className={`${isMobile ? 'rounded-2xl flex-col space-y-0' : 'rounded-full flex items-center'} shadow-xl border border-white/20 relative z-20 backdrop-blur-sm transition-all duration-300 ${activeSection ? 'bg-[#ebebeb] shadow-2xl' : 'bg-white shadow-lg hover:shadow-xl'}`}>
              {/* Where Section */}
              <div
                ref={whereRef}
                className={`${getSectionClasses('where')} ${isMobile ? 'rounded-t-2xl border-b border-gray-200 py-4' : 'mr-2 h-16'} flex items-center justify-between`}
                onClick={() => handleSectionClick('where')}
              >
                <div className="flex-1">
                  <div className="text-sm font-semibold text-search-text mb-1">Where</div>
                  {activeSection === 'where' ? (
                    <Input
                      placeholder="Search destinations..."
                      value={searchQuery || searchData.location}
                      onChange={(e) => handleDestinationInputChange(e.target.value)}
                      className="text-sm border-none p-0 h-auto bg-transparent focus:ring-0 focus:border-none shadow-none w-full"
                      autoFocus
                    />
                  ) : (
                    <div className="text-sm text-search-secondary truncate">
                      {searchData.location}
                    </div>
                  )}
                </div>
                {activeSection === 'where' && !isMobile && (searchQuery || searchData.location) && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSearchQuery('');
                      setSearchData(prev => ({ ...prev, location: '' }));
                    }}
                    className="ml-2 p-1 hover:bg-gray-100 rounded-full transition-colors flex items-center justify-center"
                  >
                    <X className="w-4 h-4 text-gray-400 hover:text-gray-600" />
                  </button>
                )}
              </div>

              {/* Divider */}
              {!isMobile && <div className="w-px h-8 bg-gray-300/50" />}

              {/* Date Section */}
              <div
                ref={dateRef}
                className={`${getSectionClasses('date')} ${isMobile ? 'border-b border-gray-200 py-4' : 'h-16'} flex items-center justify-between`}
                onClick={() => handleSectionClick('date')}
              >
                <div className="flex-1">
                  <div className="text-sm font-semibold text-search-text mb-1">When</div>
                  <div className="text-sm text-search-secondary truncate">
                    {formatTravelDate(searchData.date)}
                  </div>
                </div>
                {activeSection === 'date' && !isMobile && searchData.date && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSearchData(prev => ({ ...prev, date: '' }));
                    }}
                    className="ml-2 p-1 hover:bg-gray-100 rounded-full transition-colors flex items-center justify-center"
                  >
                    <X className="w-4 h-4 text-gray-400 hover:text-gray-600" />
                  </button>
                )}
              </div>

              {/* Divider */}
              {!isMobile && <div className="w-px h-8 bg-gray-300/50" />}

              {/* Who Section */}
              <div
                ref={whoRef}
                className={`${getSectionClasses('who')} ${isMobile ? 'rounded-b-2xl flex-col py-4' : 'flex items-center h-16 justify-between'}`}
                onClick={() => handleSectionClick('who')}
              >
                <div className="flex-1 flex items-center">
                    <div className="flex-1">
                        <div className="text-sm font-semibold text-search-text mb-1">Who</div>
                        <div className="text-sm text-search-secondary truncate">
                            {getGuestText()}
                        </div>
                    </div>
                    {activeSection === 'who' && !isMobile && (adults !== 2 || children !== 0) && (
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            dispatch(
                                selectAdultsChild({
                                room: {
                                    adult: 2,
                                    child: 0,
                                    room: 1,
                                },
                                })
                            );
                        }}
                        className="ml-2 p-1 hover:bg-gray-100 rounded-full transition-colors flex items-center justify-center"
                    >
                        <X className="w-4 h-4 text-gray-400 hover:text-gray-600" />
                    </button>
                    )}
                </div>
                {isMobile ? (
                  <div className="w-full mt-4 flex items-center justify-between">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSearchData({
                          location: '',
                          locationId: '',
                          date: `${thirtyDaysFromNow.toLocaleDateString()}`,
                          guests: 2
                        });
                        setSearchQuery('');
                      }}
                      className="text-sm text-gray-500 hover:text-gray-700 transition-colors underline"
                    >
                      Clear all
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSearch();
                      }}
                      className="bg-app-secondary text-search-accent-foreground rounded-full py-3 px-6 flex items-center justify-center gap-2 transition-all duration-300 ease-smooth hover:shadow-md"
                    >
                      <Search className="w-4 h-4" />
                      <span className="text-sm font-semibold">Search</span>
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleSearch();
                    }}
                    className={`
                      bg-app-secondary text-search-accent-foreground rounded-full flex items-center justify-center
                      transition-all duration-300 ease-smooth hover:shadow-md
                      ${activeSection === 'who'
                        ? 'px-6 py-3 gap-2'
                        : 'w-12 h-12'
                      }
                    `}
                  >
                    <Search className="w-4 h-4" />
                    {activeSection === 'who' && (
                      <span className="text-sm font-semibold whitespace-nowrap">
                        Search
                      </span>
                    )}
                  </button>
                )}
              </div>
            </div>

            {/* Dropdown Panel */}
            {activeSection && (
              <div
                className={`absolute z-10 transition-all duration-300 ease-smooth ${
                  isMobile ? 'top-full mt-2 left-0 right-0' : 'top-full mt-2'
                }`}
                style={!isMobile ? dropdownStyle : {}}
              >
                <div className={`bg-white shadow-2xl border border-gray-100 animate-in slide-in-from-top-2 fade-in duration-300 backdrop-blur-sm ${
                  isMobile ? 'rounded-2xl mx-4' : 'rounded-3xl'
                } ${
                  activeSection === 'where' ? 'p-6 max-h-[40rem] overflow-y-auto' :
                  activeSection === 'date' ? 'p-4' :
                  activeSection === 'who' ? 'p-6' : 'p-6'
                }`}>
                  {activeSection === 'where' && (
                    <WherePanel
                      searchQuery={searchQuery}
                      location={searchData.location}
                      onLocationChange={handleDestinationInputChange}
                      onLocationSelect={handleLocationSelect}
                      destinations={allDestinations}
                      isLoading={isLoading}
                      isMobile={isMobile}
                      featuredDestinations={FEATURED_DESTINATIONS}
                      clearLocation={() => {
                        setSearchQuery('');
                        setSearchData(prev => ({ ...prev, location: '' }));
                      }}
                    />
                  )}
                  {activeSection === 'date' && (
                    <DatePanel onDateSelect={handleDateSelect} />
                  )}
                  {activeSection === 'who' && (
                    <WhoPanel
                      guests={searchData.guests}
                      onGuestsChange={handleGuestsChange}
                    />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Enhanced floating elements for a more dynamic feel */}
      <div className="absolute top-20 left-10 w-28 h-28 bg-emerald-400/20 backdrop-blur-sm rounded-full animate-pulse-slow hidden lg:block"></div>
      <div className="absolute bottom-32 right-16 w-24 h-24 bg-blue-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-700 hidden lg:block"></div>
      <div className="absolute top-1/3 right-24 w-20 h-20 bg-purple-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-300 hidden lg:block"></div>
      <div className="absolute bottom-1/4 left-24 w-16 h-16 bg-yellow-400/20 backdrop-blur-sm rounded-full animate-pulse-slow delay-1000 hidden lg:block"></div>
    </section>
  );
};



export default SearchBar;
